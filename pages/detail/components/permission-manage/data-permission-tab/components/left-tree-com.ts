import {Component, DataTypes} from 'san';
import {html} from '@baiducloud/runtime';
import {Button, Tree2, Loading, Search, Select, Notification} from '@baidu/sui';
import {OutlinedRefresh} from '@baidu/sui-icon';
import {SanComputedProps} from 'types/global';
import AuthDataDialog from './authorization-data-dialog';

import '../index.less';

const klass = 'meta-catalog-collapse';

/**
 * 转换懒加载结果 to Tree2 可用数据
 * @param res 后端返回 API 数据
 * @param keyprefix 节点 key 前缀，通常为 catalog.database. 格式
 * @param isLeaf 是否为叶子节点
 */
const transformResData = (res: any, keyprefix: string = '', isLeaf: boolean = false) => {
    return res.map((item: any) => ({
        key: `${keyprefix}${item.name}`,
        label: item.name,
        isLeaf,
        customprivilegeSources: item.privilegeSources
    }));
};

/**
 * 转换搜索结果为Tree2所需的TreeData格式
 * @param searchResult 搜索接口返回的结果
 */
const transformSearchResultToTreeData = (searchResult: any) => {
    if (!searchResult || !searchResult.catalogs) {
        return [];
    }

    return searchResult.catalogs.map((catalog: any) => {
        const catalogNode = {
            key: catalog.name,
            label: catalog.name,
            customprivilegeSources: catalog.privilegeSources,
            children: []
        };

        // 处理 databases
        if (catalog.databases && catalog.databases.length > 0) {
            catalogNode.children = catalog.databases.map((database: any) => {
                const databaseNode = {
                    key: `${catalog.name}.${database.name}`,
                    label: database.name,
                    customprivilegeSources: database.privilegeSources,
                    children: [] as any[]
                };

                // 创建分类节点
                const categoryNodes: any[] = [];

                // 处理 tables
                if (database.tables && database.tables.length > 0) {
                    const tableNodes = database.tables.map((table: any) => ({
                        key: `${catalog.name}.${database.name}.table.${table.name}`,
                        label: table.name,
                        isLeaf: true,
                        customprivilegeSources: table.privilegeSources
                    }));

                    categoryNodes.push({
                        key: `${catalog.name}.${database.name}.table`,
                        label: `数据表 (${tableNodes.length})`,
                        children: tableNodes
                    });
                }

                // 处理 views
                if (database.views && database.views.length > 0) {
                    const viewNodes = database.views.map((view: any) => ({
                        key: `${catalog.name}.${database.name}.view.${view.name}`,
                        label: view.name,
                        isLeaf: true,
                        customprivilegeSources: view.privilegeSources
                    }));

                    categoryNodes.push({
                        key: `${catalog.name}.${database.name}.view`,
                        label: `视图 (${viewNodes.length})`,
                        children: viewNodes
                    });
                }

                // 处理 materializedViews
                if (database.materializedViews && database.materializedViews.length > 0) {
                    const materializedViewNodes = database.materializedViews.map((materializedView: any) => ({
                        key: `${catalog.name}.${database.name}.materializedView.${materializedView.name}`,
                        label: materializedView.name,
                        isLeaf: true,
                        customprivilegeSources: materializedView.privilegeSources
                    }));

                    categoryNodes.push({
                        key: `${catalog.name}.${database.name}.materializedView`,
                        label: `物化视图 (${materializedViewNodes.length})`,
                        children: materializedViewNodes
                    });
                }

                databaseNode.children = categoryNodes;
                return databaseNode;
            });
        }

        return catalogNode;
    });
};

/**
 * 验证和解析搜索关键词格式
 * @param keyword 搜索关键词
 * @param keywordType 搜索类型
 * @returns 解析后的参数对象或null（格式错误时）
 */
const parseSearchKeyword = (keyword: string, keywordType: string) => {
    const parts = keyword.split('.');

    switch (keywordType) {
        case 'catalog':
            if (parts.length !== 1) {
                return null;
            }
            return {
                catalogName: parts[0]
            };

        case 'database':
            if (parts.length !== 2) {
                return null;
            }
            return {
                catalogName: parts[0],
                dbName: parts[1]
            };

        case 'table':
            if (parts.length !== 3) {
                return null;
            }
            return {
                catalogName: parts[0],
                dbName: parts[1],
                tableName: parts[2]
            };

        default:
            return null;
    }
};

export default class LeftTreeCom extends Component {
    static template = html`
        <div class="${klass} main-second-sidebar drag-container ">
            <div class="${klass}-container">
                <div class="${klass}-title">
                    <s-button skin="primary" on-click="onAuthorizationData">授权数据</s-button>
                    <outlined-refresh on-click="onTreeRefresh" class="${klass}-filter-icon" />
                </div>
                <div class="${klass}-filter">
                    <s-search
                        value="{= keyword =}"
                        placeholder="{{searchPlaceholder}}"
                        on-search="onTreeSearch"
                        clearable
                        class="${klass}-filter-search"
                        size="small"
                    >
                        <s-select
                            slot="options"
                            width="{{145}}"
                            value="{=keywordType=}"
                            datasource="{{keywordTypeSource}}"
                        />
                    </s-search>
                </div>
                <div class="${klass}-content">
                    <s-loading loading="{{treeLoading}}" class="${klass}-loading" />
                    <s-tree
                        s-if="{{!treeLoading}}"
                        blockNode
                        height="{{300}}"
                        loadData="{{isSearchMode ? null : onLoadData}}"
                        treeData="{{treeData}}"
                        defaultExpandAll="{{isSearchMode}}"
                        on-select="onTreeSelect"
                    ></s-tree>
                </div>
            </div>
        </div>
    `;
    static components = {
        's-tree': Tree2,
        's-button': Button,
        's-loading': Loading,
        's-search': Search,
        's-select': Select,
        's-notification': Notification,
        'outlined-refresh': OutlinedRefresh
    };

    static DataTypes = {
        // 路由参数 route.query
        query: DataTypes.object,
        // 左侧面板Tree 选中数据
        treeSelectData: DataTypes.arrayOf(
            DataTypes.shape({
                treeLevel: DataTypes.number,
                key: DataTypes.string,
                label: DataTypes.string,
                privilegeSources: DataTypes.array
            })
        ),
        // 针对 用户详情和角色详情接口参数不同
        baseAPIParams: DataTypes.object
    };

    static computed: SanComputedProps = {
        searchPlaceholder() {
            const keywordType = this.data.get('keywordType');
            switch (keywordType) {
                case 'catalog':
                    return '搜索 catalog';
                case 'database':
                    return '搜索 catalog.database';
                case 'table':
                    return '搜索 catalog.database.table';
            }
        }
    };

    initData() {
        return {
            keywordTypeSource: [
                {text: 'Catalog', value: 'catalog'},
                {text: 'Database', value: 'database'},
                {text: '表/视图', value: 'table'}
            ],
            keyword: '',
            keywordType: 'catalog',
            treeData: [],
            treeSelectData: [],
            onLoadData: this.onLoadData.bind(this),
            treeLoading: false,
            isSearchMode: false // 标识是否为搜索模式
        };
    }

    async attached() {
        this.onTreeRefresh();
    }

    // 授权数据
    onAuthorizationData() {
        const deployId = this.data.get('query.deployId');
        const dialog = new AuthDataDialog({
            data: {
                deployId
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => {
            Notification.success('授权数据成功');
            this.onTreeRefresh();
        });
    }

    // 异步加载数据
    async onLoadData(treeNodeData: any) {
        const level = treeNodeData.treeLevel;

        if (level === 0) {
            return this.getDatabaseData(treeNodeData);
        } else if (level === 1) {
            const [table, view, materializedView] = await Promise.all([
                this.getTableData(treeNodeData),
                this.getViewData(treeNodeData),
                this.getMaterializedViewData(treeNodeData)
            ]);
            return [
                {
                    label: `数据表 (${table.length})`,
                    key: `${treeNodeData.key}.table`,
                    children: table
                },
                {
                    label: `视图 (${view.length})`,
                    key: `${treeNodeData.key}.view`,
                    children: view
                },
                {
                    label: `物化视图 (${materializedView.length})`,
                    key: `${treeNodeData.key}.materializedView`,
                    children: materializedView
                }
            ];
        }
    }

    // 刷新 Tree
    onTreeRefresh() {
        this.data.set('treeData', []);
        this.data.set('treeSelectData', []);
        this.data.set('isSearchMode', false); // 重置搜索模式
        // 存在搜索调用搜索，否则调用获取 Catalog 数据
        this.nextTick(() => {
            if (this.data.get('keyword')) {
                this.onTreeSearch();
            } else {
                this.getCatalogData();
            }
        });
    }

    // 获取 Catalog 数据
    async getCatalogData() {
        this.data.set('treeLoading', true);
        const res = await this.$http.paloPost('engineAuthCatalogList', {
            deployId: this.data.get('query.deployId'),
            ...this.data.get('baseAPIParams')
        });
        const treeData = transformResData(res.catalogPrivs || [], '');
        this.data.set('treeData', treeData);
        this.data.set('treeLoading', false);
    }

    // 获取 Database 数据
    async getDatabaseData(treeNodeData: any) {
        const res = await this.$http.paloPost('engineAuthDatabaseList', {
            deployId: this.data.get('query.deployId'),
            catalogName:  treeNodeData.key,
            ...this.data.get('baseAPIParams'),
        });
        const treeData = transformResData(res.databasePrivs || [], `${treeNodeData.key}.`);
        return treeData;
    }

    // 获取 Table 数据
    async getTableData(treeNodeData: any) {
        const [catalogName, dbName] = treeNodeData.key.split('.');
        const res = await this.$http.paloPost('engineAuthTableList', {
            deployId: this.data.get('query.deployId'),
            catalogName,
            dbName,
            ...this.data.get('baseAPIParams'),
            findTable: true
        });
        const treeData = transformResData(res.tablePrivs || [], `${treeNodeData.key}.table.`, true);
        return treeData;
    }

    // 获取 视图 数据
    async getViewData(treeNodeData: any) {
        const [catalogName, dbName] = treeNodeData.key.split('.');
        const res = await this.$http.paloPost('engineAuthTableList', {
            deployId: this.data.get('query.deployId'),
            catalogName,
            dbName,
            ...this.data.get('baseAPIParams'),
            findTable: true
        });
        const treeData = transformResData(res.tablePrivs || [], `${treeNodeData.key}.view.`, true);
        return treeData;
    }

    // 获取 物化视图 数据
    getMaterializedViewData(treeNodeData: any) {
        this.data.set('treeLoading', true);
        const [catalogName, dbName] = treeNodeData.key.split('.');
        const res = await this.$http.paloPost('engineAuthTableList', {
            deployId: this.data.get('query.deployId'),
            catalogName,
            dbName,
            ...this.data.get('baseAPIParams'),
        });
        let res = [
            {
                name: 'materializedView1'
            },
            {
                name: 'materializedView2'
            },
            {
                name: 'materializedView3'
            }
        ];
        const treeData = transformResData(res, `${treeNodeData.key}.materializedView.`, true);
        this.data.set('treeLoading', false);
        return treeData;
    }

    // 树节点选择
    onTreeSelect({info}: {info: any}) {
        let treeSelectData = [];
        let nodeData = info.node.data.get().treeNodeDataV;
        let treeLevel = nodeData.isEnd.length;
        if (treeLevel === 3) {
            // PM 确定右侧只能有一行数据，后续可优化，当点击分类时，右侧全量分页展示分类中的全部节点
            return;
        }
        treeSelectData.unshift({
            treeLevel,
            key: nodeData.key,
            label: nodeData.label,
            privilegeSources: nodeData.customprivilegeSources
        });
        this.data.set('treeSelectData', treeSelectData);
    }

    // 树搜索
    async onTreeSearch() {
        this.data.set('treeLoading', true);
        const {keywordType, keyword} = this.data.get('');

        if (!keyword) {
            this.onTreeRefresh();
            return;
        }

        // 验证和解析关键词格式
        const searchParams = parseSearchKeyword(keyword, keywordType);
        if (!searchParams) {
            let errorMessage = '';
            switch (keywordType) {
                case 'catalog':
                    errorMessage = '搜索 catalog 格式应为: catalogName';
                    break;
                case 'database':
                    errorMessage = '搜索 database 格式应为: catalogName.databaseName';
                    break;
                case 'table':
                    errorMessage = '搜索 table 格式应为: catalogName.databaseName.tableName';
                    break;
                default:
                    errorMessage = '搜索格式错误';
            }
            Notification.error(errorMessage);
            this.data.set('treeLoading', false);
            return;
        }

        try {
            // const res = await this.$http.paloPost('engineAuthSearchTree', {
            //     deployId: this.data.get('query.deployId'),
            //     ...this.data.get('baseAPIParams'),
            //     ...searchParams
            // });

            // mock 数据 - 请求回来的数据为：res
            let res = {
                "catalogs": [
                    {
                        "id": 12044,
                        "name": "hive",
                        "privilegeSources": [
                            {
                                "privilege": "SELECT",
                                "roles": [],
                                "direct": true
                            }
                        ],
                        "databases": [
                            {
                                "name": "tpch1_orc",
                                "privilegeSources": [
                                    {
                                        "privilege": "SELECT",
                                        "roles": [],
                                        "direct": true
                                    }
                                ],
                                "tables": [
                                    {
                                        "name": "customer",
                                        "privilegeSources": [
                                            {
                                                "privilege": "SELECT",
                                                "roles": [],
                                                "direct": true
                                            },
                                            {
                                                "privilege": "LOAD",
                                                "roles": ["shd_role"],
                                                "direct": false
                                            }
                                        ]
                                    }
                                ],
                                "views": [
                                    {
                                        "name": "customer"
                                    }
                                ],
                                "materializedViews": null // 没有物化视图数据
                            },
                            {
                                "name": "tpch1_parquet",
                                "tables": [
                                    {
                                        "name": "customer"
                                    }
                                ],
                                "views": null,
                                "materializedViews": null
                            }
                        ]
                    }
                ]
            };

            // 转换搜索结果为Tree数据格式
            const treeData = transformSearchResultToTreeData(res);

            // 设置为搜索模式，启用默认展开所有节点
            this.data.set('isSearchMode', true);
            this.data.set('treeData', treeData);

        } catch (error) {
            console.error('搜索失败:', error);
            // 搜索失败时回退到正常模式
            this.data.set('isSearchMode', false);
            this.data.set('treeData', []);
        } finally {
            this.data.set('treeLoading', false);
        }
    }
}
