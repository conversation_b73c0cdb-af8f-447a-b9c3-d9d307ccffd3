
/**
 * 权限管理 - 数据权限
 * <AUTHOR>
 */

import {Component, DataTypes} from 'san';
import {html} from '@baiducloud/runtime';

import LeftTreeCom from './components/left-tree-com';
import RightContentCom from './components/right-content-com';

import './index.less';
import { SanComputedProps } from 'types/global';

const klass = 'meta-manage-content';

export default class DataPermissionCom extends Component {
    static template = html`
       <div class="${klass}">
            <div class="${klass}-left">
                <left-tree-com
                    s-ref="leftTree"
                    query="{{query}}"
                    baseAPIParams="{{baseAPIParams}}"
                    treeSelectData="{=treeSelectData=}"
                />
            </div>
            <div class="${klass}-right">
                <right-content-com
                    query="{{query}}"
                    baseAPIParams="{{baseAPIParams}}"
                    treeSelectData="{=treeSelectData=}"
                    isUser="{{isUser}}"
                />
            </div>
        </div>
    `;

    static components = {
        'left-tree-com': LeftTreeCom,
        'right-content-com': RightContentCom
    };

    static DataTypes = {
        /**
         * 路由参数 route.query
         */
        query: DataTypes.object,
        /**
         * 是否为用户详情页 (角色详情页为 false)
         */
        isUser: DataTypes.bool
    };

    static computed: SanComputedProps = {
        // 针对 用户详情和角色详情接口参数不同
        baseAPIParams() {
            const {userName, host, roleName} = this.data.get('query');
            const isUser = this.data.get('isUser');
            const userIdentity = `'${userName}'@'${host}'`;
            return {
                ...isUser ? {userIdentity} : {roleName}
            };
        },
    };

    initData() {
        return {
            treeSelectData : [] // Left Tree 选中数据，在右侧面板需要
        };
    }
}

